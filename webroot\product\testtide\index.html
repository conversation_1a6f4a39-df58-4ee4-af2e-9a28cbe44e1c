<!DOCTYPE html>
<head>
    <title>Ambient Test</title>
    
    <script type="text/javascript" src="../../loader/loader.js"></script>
    <script type="text/javascript" src="../../loader/base.js"></script>
    <script type="text/javascript" src="../../loader/filesystem.js"></script>
    <script type="text/javascript" src="../../loader/config.js"></script>
    <script type="text/javascript" src="../../loader/object.js"></script>
    <script type="text/javascript" src="../../loader/module.js"></script>
    <script type="text/javascript" src="../../loader/rootmodule.js"></script>
    <script type="text/javascript" src="../../loader/requiremodule.js"></script>

    <script type="text/javascript" src="../../loader/system.js"></script>
    <script type="text/javascript" src="../../loader/world.js"></script>
    <script type="text/javascript" src="../../loader/god.js"></script>
    <script type="text/javascript" src="../../loader/ruleengine.js"></script>
    
    <script>
        var config = {
            "debugmode": true
            , "appversion": "last"
            , "productname": "sactide"
            , "langcode": "en"

            , "isserver": false

            , "productpath": ""
            , "libpath": "../../sourcery/"
            , "loaderpath": ""
                
            , "server": {
                "httpport": 80
                , "httpsport": 443
                , "webdomain": "http://localhost/"
            }

            , "basepath": {
                "product": ""
                , "shared": ""
                , "serverroot": ""
            }

            , "path": {
                "media": "media/"
                , "i18n": "interface/i18n/"
                , "data": "data/"
                , "cert": "config/cert/"
            } 
            
            , "worlds": [
                    { "name": "client", "roles": ["client", "debugmode"], "requires": ["script/"] }
            ]
        }

        _.ambient
            .start(config)  
                  
    </script>

</head>

<body>
</body>
</html>


