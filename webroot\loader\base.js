//*************************************************************************************************
// base - Copyright (c) 2024 Sorcery and Cybernetics. All rights reserved.
//*************************************************************************************************
; (function(_){
    _.debug = function (line) {
        if ((typeof Debug !== "undefined") && Debug.writeln) {
            Debug.writeln(line)
        }

        if ((typeof console !== "undefined") && console.log) {
            console.log(line)
        }
    }

    _.noop = function () { }
    _.noop._functiontype = "noop"
    
    _.isfunction = function (value) {
        return !!(value && (value.constructor === Function))
    }
    
    _.isjson = function (value) {
        return !!(value && (value.constructor === Object))
    }

    _.isstring = function(value) {
        return !!(value && (value.constructor == String))
    }

    _.isarray = function(value) {
        return !!(value && (value.constructor == Array))
    }

    _.cstr = function(str) {
        return str == null ? "" : str.toString()
    }
    
    _.normalize = function (value, context, arg) {
        return _.isfunction(value) ? value.call(context, arg) : value
    }
    
    _.sameleft$ = function (str1, str2) {
        var pos = 0
        var result = ""
        
        while ((pos < str1.length) && (pos < str2.length)) {
            if (str1.charCodeAt(pos) != str2.charCodeAt(pos)) {
                break
            }
            result += str1.charAt(pos)
            pos++
        }
        
        return result
    }

    _.trim$ = function (str) {
        return _.cstr(str).replace(/^\s+/, "").replace(/\s+$/, "")
    }  

    _.split$ = function (str, delimiter, count) {
        return str == null ? [] : str.toString().split(delimiter, count)
    }

    _.foreach = function (items, next) {
        var items = _.isarray(items) ? items : Object.keys(items)
        var index = 0

        while (index < items.length) {
            next(items[index], index)
            index++
        }
    }

    _.rofeach = function (items, next) {
        var items = _.isarray(items) ? items : Object.keys(items)
        var index = items.length

        while (index) {
            index--
            next(items[index], index)
        }
    }
    
    _.extend = function (target, source) {
        for (var key in source) {
            if (source.hasOwnProperty(key)) {
                var value = source[key]
                
                if (_.isjson(value)) {
                    if (!target[key]) { target[key] = {} }
                    if (!_.isjson(target[key])) { throw "Error in _.extend: target[" + key + "] is not an object." }
                    _.extend(target[key], value)
                } else {
                    target[key] = value
                }
            }
        }
    }

    _.merge = function (target, source) {
        var result = {}

        _.extend(result, target)
        _.extend(result, source)
        
        return result
    }

    //Async foreach function using callback method
    _.foreachasync = function (items, fn, finish) {
        //first if items not array, convert to array
        if (!_.isarray(items)) {
            var newitems = []
            _.foreach(items, function (item) { newitems.push(item) })
            items = newitems
        }

        if (items.length == 0) {
            if (finish) { finish() }
            return
        }

        //execute function for each item in the array
        var execute = function (index) {
            if (index >= items.length) {
                if (finish) { finish() }
                return
            }

            var item = items[index]
            fn(item, function () { execute(index + 1) })
        }

        execute(0)        
    }


}) (_.ambient)

