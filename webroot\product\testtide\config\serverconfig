﻿{
  "debugmode": true
  , "devmode": true
  , "appversion": "last"
  , "productname": "sactide"
  , "langcode": "en"
    
  , "server": {
      "httpport": 80
      , "httpsport": 443
      , "webdomain": "http://localhost/"

      , "sessiontimeout": 3600
      , "responsetimeout": 3000

      , "sender": "<EMAIL>"
  }

  , "basepath": {
      "product": ""
      , "shared": ""
      , "serverroot": ""
  }

  , "path": {
      "media": "media/"
      , "i18n": "interface/i18n/"
      , "data": "data/"
      , "cert": "config/cert/"
  } 
  
  , "worlds": [
        { "name": "server", "roles": ["server", "debugmode"], "requires": ["script/"] }
        //        , { "name": "client", "roles": ["client"], "requires": ["script/"] }
  ]
}
