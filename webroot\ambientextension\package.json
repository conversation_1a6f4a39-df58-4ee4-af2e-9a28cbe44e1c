{"name": "ambientextension", "displayName": "Ambient Extension", "description": "Developer tools for Ambient", "version": "0.1.0", "publisher": "Ambient", "engines": {"vscode": "^1.98.0"}, "categories": ["Other"], "activationEvents": ["*"], "main": "./extension.js", "contributes": {"views": {"explorer": [{"id": "ambientextension.filehistory", "name": "File History", "icon": "$(symbol-method)"}, {"id": "ambientextension.methodtree", "name": "Class Method Explorer", "icon": "$(symbol-method)"}]}, "commands": [{"command": "ambientextension.gotoLine", "title": "Go to Line"}]}}