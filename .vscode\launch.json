{"version": "0.2.0", "compounds": [{"name": "Full Launch", "configurations": ["Launch Client", "Launch Server"]}], "configurations": [{"name": "Launch Client", "request": "launch", "type": "chrome", "url": "http://localhost:80", "webRoot": "${workspaceFolder}/webroot/product/testtide", "file": "${workspaceFolder}/webroot/product/testtide/index.html"}, {"name": "Launch Server", "type": "node", "request": "launch", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/webroot/product/testtide/server.js", "cwd": "${workspaceFolder}/webroot/product/testtide/", "console": "integratedTerminal"}, {"name": "Ambient Extension", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}/webroot/ambientextension"]}]}