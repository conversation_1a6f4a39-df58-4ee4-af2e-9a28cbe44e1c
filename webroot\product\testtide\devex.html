<!DOCTYPE html>
<html translate="yes" language="nl">
<head>
    <title>Ambient Test</title>
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <base target="_blank">
    <!--<link rel="apple-touch-icon" href="favicon.png">
    <link rel="manifest" href="manifest.dev.json">
    <link rel="shortcut icon" sizes="128x128" href="favicon128.png">-->

    <meta charset="utf-8" />
    <script type="text/javascript" src="../../loader/xxx.js"></script>
    <script>
        // global._ = global._ || {}
        // _ = global._


        // var normalizepath = function (path) {
        //     return (path || "").replace(/\\/g, "/")
        // }

        // var productpath = normalizepath(process.cwd()) + "/"

        // var config = {
        //     isserver: true
            
        //     , productpath: productpath
        //     , libpath: productpath + "../../sourcery/"
        //     , loaderpath: product//path + "../../"
        // }

        // require(config.loaderpath + "loader/_root.js")
        // _.ambient
        //     .start(config)        
        //     </script>

</head>

<body>
</body>
</html>


